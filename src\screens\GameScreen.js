import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  Animated,
  BackHandler
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { SafeAreaView } from 'react-native-safe-area-context';
import * as Haptics from 'expo-haptics';
import { Audio } from 'expo-av';
import { useGame } from '../context/GameContext';
import { getThemeColors, getGameItemsByMode, getDifficultySettings } from '../utils/themes';

const { width, height } = Dimensions.get('window');

export default function GameScreen({ navigation }) {
  const { state, dispatch, GAME_STATES } = useGame();
  const [currentTarget, setCurrentTarget] = useState(null);
  const [currentItem, setCurrentItem] = useState(null);
  const [isActive, setIsActive] = useState(false);
  const [feedback, setFeedback] = useState(null);
  const [timeLeft, setTimeLeft] = useState(0);
  
  const themeColors = getThemeColors(state.theme);
  const gameItems = getGameItemsByMode(state.gameMode);
  const difficultySettings = getDifficultySettings(state.settings.difficulty);
  
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const timeoutRef = useRef(null);
  const intervalRef = useRef(null);

  // Manejar botón de retroceso de Android
  useEffect(() => {
    const backAction = () => {
      pauseGame();
      return true;
    };

    const backHandler = BackHandler.addEventListener('hardwareBackPress', backAction);
    return () => backHandler.remove();
  }, []);

  // Generar nueva ronda
  const generateNewRound = useCallback(() => {
    const items = gameItems.items;
    const targetIndex = Math.floor(Math.random() * items.length);
    const target = items[targetIndex];

    // Ajustar probabilidad según el nivel y modo
    let correctProbability = 0.3; // 30% base

    if (state.gameMode === 'patterns') {
      // En modo patrones, más engañoso
      correctProbability = 0.4 + (state.level * 0.05); // Aumenta con el nivel
    } else if (state.gameMode === 'sounds') {
      // En modo sonidos, un poco más difícil
      correctProbability = 0.35;
    }

    const showCorrect = Math.random() < Math.min(correctProbability, 0.6); // Máximo 60%
    let currentItem;

    if (showCorrect) {
      currentItem = target;
    } else {
      const availableItems = items.filter(item => item !== target);
      currentItem = availableItems[Math.floor(Math.random() * availableItems.length)];
    }

    setCurrentTarget(target);
    setCurrentItem(currentItem);
    setIsActive(true);
    setTimeLeft(state.gameSpeed);
    
    // Animación de entrada
    Animated.sequence([
      Animated.timing(scaleAnim, {
        toValue: 0.8,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
      }),
    ]).start();

    // Pulso continuo
    Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.1,
          duration: 500,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 500,
          useNativeDriver: true,
        }),
      ])
    ).start();
    
    // Countdown timer
    intervalRef.current = setInterval(() => {
      setTimeLeft(prev => {
        if (prev <= 100) {
          clearInterval(intervalRef.current);
          return 0;
        }
        return prev - 100;
      });
    }, 100);
    
    // Auto-advance after gameSpeed milliseconds
    timeoutRef.current = setTimeout(() => {
      if (isActive) {
        handleTimeOut(currentItem, target);
      }
    }, state.gameSpeed);
  }, [state.gameSpeed, isActive, gameItems.items]);

  // Manejar timeout
  const handleTimeOut = (item, target) => {
    setIsActive(false);
    clearTimeout(timeoutRef.current);
    clearInterval(intervalRef.current);
    pulseAnim.stopAnimation();
    
    if (item !== target) {
      // Player didn't click and it was the wrong item - they lose a life
      handleWrongAction();
    } else {
      // Player didn't click and it was the correct item - they get points
      handleCorrectAction();
    }
  };

  // Manejar click del jugador
  const handlePlayerClick = () => {
    if (!isActive) return;
    
    setIsActive(false);
    clearTimeout(timeoutRef.current);
    clearInterval(intervalRef.current);
    pulseAnim.stopAnimation();
    
    // Vibración táctil
    if (state.settings.vibrationEnabled) {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }
    
    // Player clicked
    if (currentItem === currentTarget) {
      // Clicked on correct item - this is wrong in inverse logic
      handleWrongAction();
    } else {
      // Clicked on wrong item - this is correct in inverse logic
      handleCorrectAction();
    }
  };

  const handleCorrectAction = () => {
    const points = 10 * state.level;
    dispatch({ type: 'UPDATE_SCORE', payload: points });
    setFeedback({ type: 'correct', message: '¡Perfecto!', points });
    
    // Vibración de éxito
    if (state.settings.vibrationEnabled) {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    }
    
    // Verificar si sube de nivel
    const newScore = state.score + points;
    if (newScore % difficultySettings.pointsPerLevel === 0) {
      dispatch({ type: 'LEVEL_UP' });
      setFeedback({ type: 'levelUp', message: `¡Nivel ${state.level + 1}!`, points });
    }
    
    setTimeout(() => {
      setFeedback(null);
      if (state.gameState === GAME_STATES.PLAYING) {
        generateNewRound();
      }
    }, 1500);
  };

  const handleWrongAction = () => {
    dispatch({ type: 'LOSE_LIFE' });
    setFeedback({ type: 'wrong', message: '¡Piensa al revés!' });
    
    // Vibración de error
    if (state.settings.vibrationEnabled) {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
    }
    
    setTimeout(() => {
      setFeedback(null);
      if (state.lives > 1 && state.gameState === GAME_STATES.PLAYING) {
        generateNewRound();
      }
    }, 1500);
  };

  const pauseGame = () => {
    dispatch({ type: 'SET_GAME_STATE', payload: GAME_STATES.PAUSED });
    setIsActive(false);
    clearTimeout(timeoutRef.current);
    clearInterval(intervalRef.current);
    pulseAnim.stopAnimation();
  };

  const resumeGame = () => {
    dispatch({ type: 'SET_GAME_STATE', payload: GAME_STATES.PLAYING });
    generateNewRound();
  };

  const quitGame = () => {
    // Guardar high score si es necesario
    if (state.score > state.highScores[state.gameMode]) {
      dispatch({ 
        type: 'SET_HIGH_SCORE', 
        gameMode: state.gameMode, 
        score: state.score 
      });
    }
    
    dispatch({ type: 'SET_GAME_STATE', payload: GAME_STATES.MENU });
    navigation.goBack();
  };

  const restartGame = () => {
    dispatch({ type: 'RESET_GAME' });
    dispatch({ type: 'START_GAME' });
    generateNewRound();
  };

  // Iniciar juego
  useEffect(() => {
    if (state.gameState === GAME_STATES.PLAYING && !currentTarget) {
      generateNewRound();
    }
  }, [state.gameState, generateNewRound, currentTarget]);

  // Limpiar timers al desmontar
  useEffect(() => {
    return () => {
      clearTimeout(timeoutRef.current);
      clearInterval(intervalRef.current);
    };
  }, []);

  const renderLives = () => {
    return Array.from({ length: 3 }, (_, index) => (
      <Text 
        key={index} 
        style={[
          styles.life,
          { opacity: index < state.lives ? 1 : 0.3 }
        ]}
      >
        ❤️
      </Text>
    ));
  };

  const getProgressWidth = () => {
    return (timeLeft / state.gameSpeed) * 100;
  };

  return (
    <LinearGradient
      colors={themeColors.background}
      style={styles.container}
    >
      <SafeAreaView style={styles.safeArea}>
        {/* Header con stats */}
        <View style={styles.header}>
          <View style={styles.statsContainer}>
            <View style={styles.statItem}>
              <Text style={[styles.statLabel, { color: themeColors.textSecondary }]}>
                Puntos
              </Text>
              <Text style={[styles.statValue, { color: themeColors.text }]}>
                {state.score}
              </Text>
            </View>
            
            <View style={styles.statItem}>
              <Text style={[styles.statLabel, { color: themeColors.textSecondary }]}>
                Nivel
              </Text>
              <Text style={[styles.statValue, { color: themeColors.text }]}>
                {state.level}
              </Text>
            </View>
            
            <View style={styles.statItem}>
              <Text style={[styles.statLabel, { color: themeColors.textSecondary }]}>
                Vidas
              </Text>
              <View style={styles.livesContainer}>
                {renderLives()}
              </View>
            </View>
          </View>
          
          <TouchableOpacity
            style={[styles.pauseButton, { backgroundColor: themeColors.card }]}
            onPress={pauseGame}
            activeOpacity={0.7}
          >
            <Text style={styles.pauseIcon}>⏸️</Text>
          </TouchableOpacity>
        </View>

        {state.gameState === GAME_STATES.PLAYING && (
          <View style={styles.gameArea}>
            {/* Objetivo */}
            <View style={styles.targetSection}>
              <Text style={[styles.targetLabel, { color: themeColors.text }]}>
                Objetivo: NO toques
              </Text>
              <View style={[styles.targetContainer, { backgroundColor: themeColors.card }]}>
                <Text style={styles.targetItem}>{currentTarget}</Text>
              </View>
            </View>

            {/* Barra de progreso */}
            <View style={styles.progressContainer}>
              <View style={[styles.progressBar, { backgroundColor: themeColors.card }]}>
                <View 
                  style={[
                    styles.progressFill, 
                    { 
                      backgroundColor: themeColors.primary,
                      width: `${getProgressWidth()}%`
                    }
                  ]} 
                />
              </View>
            </View>

            {/* Item actual */}
            <View style={styles.currentItemSection}>
              <Animated.View
                style={[
                  styles.currentItemContainer,
                  {
                    backgroundColor: themeColors.card,
                    transform: [
                      { scale: scaleAnim },
                      { scale: pulseAnim }
                    ]
                  }
                ]}
              >
                <TouchableOpacity
                  style={styles.currentItemTouchable}
                  onPress={handlePlayerClick}
                  activeOpacity={0.8}
                  disabled={!isActive}
                >
                  <Text style={styles.currentItem}>{currentItem}</Text>
                </TouchableOpacity>
              </Animated.View>
              
              {isActive && (
                <Text style={[styles.instruction, { color: themeColors.textSecondary }]}>
                  {currentItem === currentTarget ? 
                    "¡NO TOQUES! Es el objetivo" : 
                    "¡TOCA! No es el objetivo"
                  }
                </Text>
              )}
            </View>
          </View>
        )}

        {/* Feedback */}
        {feedback && (
          <View style={styles.feedbackContainer}>
            <Text style={[
              styles.feedbackMessage,
              { 
                color: feedback.type === 'correct' || feedback.type === 'levelUp' ? 
                  themeColors.success : themeColors.error 
              }
            ]}>
              {feedback.message}
            </Text>
            {feedback.points && (
              <Text style={[styles.feedbackPoints, { color: themeColors.success }]}>
                +{feedback.points}
              </Text>
            )}
          </View>
        )}
      </SafeAreaView>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 15,
  },
  statsContainer: {
    flexDirection: 'row',
    flex: 1,
  },
  statItem: {
    alignItems: 'center',
    marginRight: 30,
  },
  statLabel: {
    fontSize: 12,
    fontWeight: '500',
  },
  statValue: {
    fontSize: 20,
    fontWeight: 'bold',
    marginTop: 2,
  },
  livesContainer: {
    flexDirection: 'row',
    marginTop: 2,
  },
  life: {
    fontSize: 16,
    marginHorizontal: 1,
  },
  pauseButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
  },
  pauseIcon: {
    fontSize: 20,
  },
  gameArea: {
    flex: 1,
    paddingHorizontal: 20,
    justifyContent: 'space-around',
  },
  targetSection: {
    alignItems: 'center',
    marginBottom: 30,
  },
  targetLabel: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 15,
  },
  targetContainer: {
    width: 100,
    height: 100,
    borderRadius: 50,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  targetItem: {
    fontSize: 50,
  },
  progressContainer: {
    paddingHorizontal: 20,
    marginBottom: 30,
  },
  progressBar: {
    height: 8,
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
  currentItemSection: {
    alignItems: 'center',
    flex: 1,
    justifyContent: 'center',
  },
  currentItemContainer: {
    width: 200,
    height: 200,
    borderRadius: 100,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 5 },
    shadowOpacity: 0.34,
    shadowRadius: 6.27,
  },
  currentItemTouchable: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 100,
  },
  currentItem: {
    fontSize: 80,
  },
  instruction: {
    fontSize: 16,
    fontWeight: '600',
    marginTop: 20,
    textAlign: 'center',
    paddingHorizontal: 20,
  },
  feedbackContainer: {
    position: 'absolute',
    top: height * 0.4,
    left: 0,
    right: 0,
    alignItems: 'center',
    zIndex: 1000,
  },
  feedbackMessage: {
    fontSize: 28,
    fontWeight: 'bold',
    textAlign: 'center',
    textShadowColor: 'rgba(0,0,0,0.3)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  feedbackPoints: {
    fontSize: 24,
    fontWeight: 'bold',
    marginTop: 5,
    textShadowColor: 'rgba(0,0,0,0.3)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
});
