import { Audio } from 'expo-av';

class SoundManager {
  constructor() {
    this.sounds = {};
    this.isEnabled = true;
    this.isInitialized = false;
    this.backgroundMusic = null;
    this.isMusicPlaying = false;
  }

  async initialize() {
    if (this.isInitialized) return;

    try {
      // Configurar el modo de audio solo para Android (simplificado)
      await Audio.setAudioModeAsync({
        staysActiveInBackground: false,
        shouldDuckAndroid: true,
        playThroughEarpieceAndroid: false
      });

      // Cargar todos los sonidos
      await this.loadSounds();
      this.isInitialized = true;
      console.log('✅ Sound manager initialized successfully for Android');
    } catch (error) {
      console.error('❌ Error initializing sound manager:', error);
      // Intentar inicializar sin configuración de audio si falla
      try {
        console.log('🔄 Trying fallback initialization...');
        await this.loadSounds();
        this.isInitialized = true;
        console.log('✅ Sound manager initialized with fallback method');
      } catch (fallbackError) {
        console.error('❌ Fallback initialization also failed:', fallbackError);
        // Inicializar en modo silencioso
        this.isInitialized = true;
        console.log('🔇 Sound manager initialized in silent mode');
      }
    }
  }

  async loadSounds() {
    try {
      console.log('🎵 Loading sounds for Android (simplified)...');

      // Usar sistema simplificado que no depende de archivos WAV
      this.sounds = {
        correct: { loaded: true, type: 'correct', frequency: 800, duration: 0.2, waveType: 'sine' },
        wrong: { loaded: true, type: 'wrong', frequency: 200, duration: 0.4, waveType: 'sawtooth' },
        levelUp: { loaded: true, type: 'levelUp', frequencies: [523, 659, 784, 1047], duration: 0.15 },
        gameOver: { loaded: true, type: 'gameOver', frequencies: [400, 350, 300, 250], duration: 0.3 },
        click: { loaded: true, type: 'click', frequency: 1000, duration: 0.1, waveType: 'square' },
        tick: { loaded: true, type: 'tick', frequency: 1200, duration: 0.05, waveType: 'sine' },
        newRecord: { loaded: true, type: 'newRecord', frequencies: [523, 659, 784, 1047, 1319], duration: 0.2 }
      };

      this.backgroundMusic = { loaded: true, type: 'background' };

      console.log('✅ All sounds loaded successfully (simplified mode)');

    } catch (error) {
      console.error('❌ Error loading sounds:', error);
      // Crear sonidos dummy si falla
      this.sounds = {
        correct: { loaded: false, type: 'correct' },
        wrong: { loaded: false, type: 'wrong' },
        levelUp: { loaded: false, type: 'levelUp' },
        gameOver: { loaded: false, type: 'gameOver' },
        click: { loaded: false, type: 'click' },
        tick: { loaded: false, type: 'tick' },
        newRecord: { loaded: false, type: 'newRecord' }
      };
      this.backgroundMusic = { loaded: false, type: 'background' };
    }
  }





  // Métodos públicos para reproducir sonidos
  async playSound(soundName, options = {}) {
    if (!this.isEnabled || !this.isInitialized) return;

    try {
      const soundObj = this.sounds[soundName];
      if (soundObj && soundObj.loaded) {
        console.log(`🔊 Playing sound: ${soundName}`);

        // Usar Web Audio API directamente
        this.playWebAudioSound(soundObj);
      } else {
        console.log(`⚠️ Sound ${soundName} not loaded`);
      }
    } catch (error) {
      console.error(`❌ Error playing sound ${soundName}:`, error);
    }
  }

  // Reproducir sonido usando Web Audio API
  playWebAudioSound(soundObj) {
    try {
      // Verificar si Web Audio está disponible
      if (typeof window === 'undefined' || (!window.AudioContext && !window.webkitAudioContext)) {
        console.log(`📱 Web Audio not available for ${soundObj.type}`);
        return;
      }

      const audioContext = new (window.AudioContext || window.webkitAudioContext)();

      if (soundObj.frequencies) {
        // Es una melodía
        console.log(`🎵 Playing melody: ${soundObj.type}`);
        this.playMelody(audioContext, soundObj.frequencies, soundObj.duration);
      } else {
        // Es un tono simple
        console.log(`🔊 Playing tone: ${soundObj.type} at ${soundObj.frequency}Hz`);
        this.playTone(audioContext, soundObj.frequency, soundObj.duration, soundObj.waveType);
      }
    } catch (error) {
      console.error('Error with Web Audio API:', error);
    }
  }

  // Reproducir un tono simple
  playTone(audioContext, frequency, duration, waveType = 'sine') {
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);

    oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);
    oscillator.type = waveType;

    // Configurar volumen con envelope
    gainNode.gain.setValueAtTime(0, audioContext.currentTime);
    gainNode.gain.linearRampToValueAtTime(0.3, audioContext.currentTime + 0.01);
    gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + duration);

    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + duration);
  }

  // Reproducir una melodía
  playMelody(audioContext, frequencies, noteDuration) {
    frequencies.forEach((frequency, index) => {
      const startTime = audioContext.currentTime + (index * noteDuration);
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();

      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);

      oscillator.frequency.setValueAtTime(frequency, startTime);
      oscillator.type = 'sine';

      // Envelope para cada nota
      gainNode.gain.setValueAtTime(0, startTime);
      gainNode.gain.linearRampToValueAtTime(0.2, startTime + 0.01);
      gainNode.gain.exponentialRampToValueAtTime(0.001, startTime + noteDuration);

      oscillator.start(startTime);
      oscillator.stop(startTime + noteDuration);
    });
  }

  playWebAudioTone(soundName) {
    try {
      const audioContext = new (window.AudioContext || window.webkitAudioContext)();
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();

      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);

      // Configurar diferentes tonos para diferentes sonidos
      switch (soundName) {
        case 'correct':
          oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
          oscillator.type = 'sine';
          gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
          gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);
          break;
        case 'wrong':
          oscillator.frequency.setValueAtTime(200, audioContext.currentTime);
          oscillator.type = 'sawtooth';
          gainNode.gain.setValueAtTime(0.2, audioContext.currentTime);
          gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);
          break;
        case 'levelUp':
          // Secuencia de tonos ascendentes
          oscillator.frequency.setValueAtTime(523, audioContext.currentTime);
          oscillator.frequency.setValueAtTime(659, audioContext.currentTime + 0.1);
          oscillator.frequency.setValueAtTime(784, audioContext.currentTime + 0.2);
          oscillator.frequency.setValueAtTime(1047, audioContext.currentTime + 0.3);
          oscillator.type = 'sine';
          gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
          gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.6);
          break;
        case 'click':
          oscillator.frequency.setValueAtTime(1000, audioContext.currentTime);
          oscillator.type = 'square';
          gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
          gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);
          break;
        case 'tick':
          oscillator.frequency.setValueAtTime(1200, audioContext.currentTime);
          oscillator.type = 'sine';
          gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
          gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.05);
          break;
        case 'newRecord':
          // Melodía especial para nuevo récord
          oscillator.frequency.setValueAtTime(523, audioContext.currentTime);
          oscillator.frequency.setValueAtTime(659, audioContext.currentTime + 0.15);
          oscillator.frequency.setValueAtTime(784, audioContext.currentTime + 0.3);
          oscillator.frequency.setValueAtTime(1047, audioContext.currentTime + 0.45);
          oscillator.frequency.setValueAtTime(1319, audioContext.currentTime + 0.6);
          oscillator.type = 'sine';
          gainNode.gain.setValueAtTime(0.4, audioContext.currentTime);
          gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 1.0);
          break;
        case 'gameOver':
          // Secuencia descendente
          oscillator.frequency.setValueAtTime(400, audioContext.currentTime);
          oscillator.frequency.setValueAtTime(350, audioContext.currentTime + 0.3);
          oscillator.frequency.setValueAtTime(300, audioContext.currentTime + 0.6);
          oscillator.frequency.setValueAtTime(250, audioContext.currentTime + 0.9);
          oscillator.type = 'sawtooth';
          gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
          gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 1.2);
          break;
        default:
          oscillator.frequency.setValueAtTime(440, audioContext.currentTime);
          oscillator.type = 'sine';
          gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
          gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);
      }

      oscillator.start(audioContext.currentTime);
      oscillator.stop(audioContext.currentTime + (soundName === 'newRecord' ? 1.0 : soundName === 'gameOver' ? 1.2 : 0.5));

    } catch (error) {
      console.error('Error playing web audio tone:', error);
    }
  }

  async startBackgroundMusic() {
    if (!this.isEnabled || !this.backgroundMusic || this.isMusicPlaying) return;

    try {
      console.log('🎵 Starting background music');
      this.isMusicPlaying = true;

      // Usar Web Audio API para música de fondo
      if (typeof window !== 'undefined' && (window.AudioContext || window.webkitAudioContext)) {
        this.startWebAudioBackgroundMusic();
      } else {
        console.log('📱 Background music not available on this platform');
      }
    } catch (error) {
      console.error('❌ Error starting background music:', error);
      this.isMusicPlaying = false;
    }
  }

  startWebAudioBackgroundMusic() {
    if (this.backgroundAudioContext) {
      this.backgroundAudioContext.close();
    }

    try {
      this.backgroundAudioContext = new (window.AudioContext || window.webkitAudioContext)();

      // Crear un loop simple y adictivo
      const playChord = (frequencies, startTime, duration) => {
        frequencies.forEach(freq => {
          const oscillator = this.backgroundAudioContext.createOscillator();
          const gainNode = this.backgroundAudioContext.createGain();

          oscillator.connect(gainNode);
          gainNode.connect(this.backgroundAudioContext.destination);

          oscillator.frequency.setValueAtTime(freq, startTime);
          oscillator.type = 'sine';
          gainNode.gain.setValueAtTime(0.015, startTime); // Volumen muy bajo para fondo
          gainNode.gain.exponentialRampToValueAtTime(0.001, startTime + duration);

          oscillator.start(startTime);
          oscillator.stop(startTime + duration);
        });
      };

      // Progresión de acordes adictiva (vi-IV-I-V)
      const chords = [
        [220.00, 261.63], // A minor (simplificado)
        [174.61, 220.00], // F major
        [261.63, 329.63], // C major
        [196.00, 246.94]  // G major
      ];

      const chordDuration = 1.5; // 1.5 segundos por acorde para ritmo más dinámico
      const currentTime = this.backgroundAudioContext.currentTime;

      // Programar los acordes
      chords.forEach((chord, index) => {
        playChord(chord, currentTime + (index * chordDuration), chordDuration);
      });

      // Programar repetición automática
      setTimeout(() => {
        if (this.isMusicPlaying) {
          this.startWebAudioBackgroundMusic();
        }
      }, chords.length * chordDuration * 1000);

    } catch (error) {
      console.error('Error starting web audio background music:', error);
    }
  }

  async stopBackgroundMusic() {
    if (!this.isMusicPlaying) return;

    try {
      console.log('🔇 Stopping background music');
      this.isMusicPlaying = false;

      if (this.backgroundMusic && this.backgroundMusic.loaded && this.backgroundMusic.sound) {
        await this.backgroundMusic.sound.stopAsync();
      }

      if (this.backgroundAudioContext) {
        this.backgroundAudioContext.close();
        this.backgroundAudioContext = null;
      }
    } catch (error) {
      console.error('❌ Error stopping background music:', error);
      this.isMusicPlaying = false;
    }
  }

  setEnabled(enabled) {
    this.isEnabled = enabled;
    if (!enabled && this.isMusicPlaying) {
      this.stopBackgroundMusic();
    }
  }

  async cleanup() {
    try {
      console.log('🧹 Cleaning up sound manager...');

      // Detener música de fondo
      await this.stopBackgroundMusic();

      // Limpiar contexto de audio
      if (this.backgroundAudioContext) {
        this.backgroundAudioContext.close();
        this.backgroundAudioContext = null;
      }

      this.sounds = {};
      this.backgroundMusic = null;
      this.isInitialized = false;
      this.isMusicPlaying = false;

      console.log('✅ Sound manager cleaned up successfully');
    } catch (error) {
      console.error('❌ Error cleaning up sounds:', error);
    }
  }
}

export default new SoundManager();
