import { Audio } from 'expo-av';

class SoundManager {
  constructor() {
    this.sounds = {};
    this.isEnabled = true;
    this.isInitialized = false;
    this.backgroundMusic = null;
    this.isMusicPlaying = false;
  }

  async initialize() {
    if (this.isInitialized) return;

    try {
      // Configurar el modo de audio con valores válidos
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: false,
        staysActiveInBackground: false,
        interruptionModeIOS: Audio.InterruptionModeIOS.DoNotMix,
        playsInSilentModeIOS: true,
        shouldDuckAndroid: true,
        interruptionModeAndroid: Audio.InterruptionModeAndroid.DoNotMix,
        playThroughEarpieceAndroid: false
      });

      // <PERSON>gar todos los sonidos
      await this.loadSounds();
      this.isInitialized = true;
    } catch (error) {
      console.error('Error initializing sound manager:', error);
      // Intentar inicializar sin configuración de audio si falla
      try {
        await this.loadSounds();
        this.isInitialized = true;
      } catch (fallbackError) {
        console.error('Fallback initialization also failed:', fallbackError);
      }
    }
  }

  async loadSounds() {
    try {
      // Usar archivos de audio simples o crear sonidos básicos
      // Por ahora, vamos a simular los sonidos con console.log para debug
      // y usar vibraciones como feedback principal

      this.sounds.correct = { loaded: true, type: 'correct' };
      this.sounds.wrong = { loaded: true, type: 'wrong' };
      this.sounds.levelUp = { loaded: true, type: 'levelUp' };
      this.sounds.gameOver = { loaded: true, type: 'gameOver' };
      this.sounds.click = { loaded: true, type: 'click' };
      this.sounds.tick = { loaded: true, type: 'tick' };
      this.sounds.newRecord = { loaded: true, type: 'newRecord' };

      // Para la música de fondo, vamos a usar un tono simple
      this.backgroundMusic = { loaded: true, type: 'background', playing: false };

    } catch (error) {
      console.error('Error loading sounds:', error);
    }
  }



  // Métodos públicos para reproducir sonidos
  async playSound(soundName, options = {}) {
    if (!this.isEnabled || !this.isInitialized) return;

    try {
      const sound = this.sounds[soundName];
      if (sound && sound.loaded) {
        // Por ahora, simular sonidos con diferentes patrones de vibración
        // En una implementación completa, aquí iríamos los archivos de audio reales
        console.log(`Playing sound: ${soundName}`);

        // Crear diferentes "sonidos" usando Web Audio API básica si está disponible
        if (typeof window !== 'undefined' && window.AudioContext) {
          this.playWebAudioTone(soundName);
        }
      }
    } catch (error) {
      console.error(`Error playing sound ${soundName}:`, error);
    }
  }

  playWebAudioTone(soundName) {
    try {
      const audioContext = new (window.AudioContext || window.webkitAudioContext)();
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();

      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);

      // Configurar diferentes tonos para diferentes sonidos
      switch (soundName) {
        case 'correct':
          oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
          oscillator.type = 'sine';
          gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
          gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);
          break;
        case 'wrong':
          oscillator.frequency.setValueAtTime(200, audioContext.currentTime);
          oscillator.type = 'sawtooth';
          gainNode.gain.setValueAtTime(0.2, audioContext.currentTime);
          gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);
          break;
        case 'levelUp':
          // Secuencia de tonos ascendentes
          oscillator.frequency.setValueAtTime(523, audioContext.currentTime);
          oscillator.frequency.setValueAtTime(659, audioContext.currentTime + 0.1);
          oscillator.frequency.setValueAtTime(784, audioContext.currentTime + 0.2);
          oscillator.frequency.setValueAtTime(1047, audioContext.currentTime + 0.3);
          oscillator.type = 'sine';
          gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
          gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.6);
          break;
        case 'click':
          oscillator.frequency.setValueAtTime(1000, audioContext.currentTime);
          oscillator.type = 'square';
          gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
          gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);
          break;
        case 'tick':
          oscillator.frequency.setValueAtTime(1200, audioContext.currentTime);
          oscillator.type = 'sine';
          gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
          gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.05);
          break;
        case 'newRecord':
          // Melodía especial para nuevo récord
          oscillator.frequency.setValueAtTime(523, audioContext.currentTime);
          oscillator.frequency.setValueAtTime(659, audioContext.currentTime + 0.15);
          oscillator.frequency.setValueAtTime(784, audioContext.currentTime + 0.3);
          oscillator.frequency.setValueAtTime(1047, audioContext.currentTime + 0.45);
          oscillator.frequency.setValueAtTime(1319, audioContext.currentTime + 0.6);
          oscillator.type = 'sine';
          gainNode.gain.setValueAtTime(0.4, audioContext.currentTime);
          gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 1.0);
          break;
        case 'gameOver':
          // Secuencia descendente
          oscillator.frequency.setValueAtTime(400, audioContext.currentTime);
          oscillator.frequency.setValueAtTime(350, audioContext.currentTime + 0.3);
          oscillator.frequency.setValueAtTime(300, audioContext.currentTime + 0.6);
          oscillator.frequency.setValueAtTime(250, audioContext.currentTime + 0.9);
          oscillator.type = 'sawtooth';
          gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
          gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 1.2);
          break;
        default:
          oscillator.frequency.setValueAtTime(440, audioContext.currentTime);
          oscillator.type = 'sine';
          gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
          gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);
      }

      oscillator.start(audioContext.currentTime);
      oscillator.stop(audioContext.currentTime + (soundName === 'newRecord' ? 1.0 : soundName === 'gameOver' ? 1.2 : 0.5));

    } catch (error) {
      console.error('Error playing web audio tone:', error);
    }
  }

  async startBackgroundMusic() {
    if (!this.isEnabled || !this.backgroundMusic || this.isMusicPlaying) return;

    try {
      console.log('Starting background music');
      this.isMusicPlaying = true;

      // Iniciar música de fondo simple si estamos en web
      if (typeof window !== 'undefined' && window.AudioContext) {
        this.startWebAudioBackgroundMusic();
      }
    } catch (error) {
      console.error('Error starting background music:', error);
    }
  }

  startWebAudioBackgroundMusic() {
    if (this.backgroundAudioContext) {
      this.backgroundAudioContext.close();
    }

    try {
      this.backgroundAudioContext = new (window.AudioContext || window.webkitAudioContext)();

      // Crear un loop simple y relajante
      const playChord = (frequencies, startTime, duration) => {
        frequencies.forEach(freq => {
          const oscillator = this.backgroundAudioContext.createOscillator();
          const gainNode = this.backgroundAudioContext.createGain();

          oscillator.connect(gainNode);
          gainNode.connect(this.backgroundAudioContext.destination);

          oscillator.frequency.setValueAtTime(freq, startTime);
          oscillator.type = 'sine';
          gainNode.gain.setValueAtTime(0.02, startTime); // Muy bajo volumen
          gainNode.gain.exponentialRampToValueAtTime(0.001, startTime + duration);

          oscillator.start(startTime);
          oscillator.stop(startTime + duration);
        });
      };

      // Progresión de acordes relajante
      const chords = [
        [261.63, 329.63, 392.00], // C major
        [220.00, 261.63, 329.63], // A minor
        [174.61, 220.00, 261.63], // F major
        [196.00, 246.94, 293.66]  // G major
      ];

      const chordDuration = 2; // 2 segundos por acorde
      const currentTime = this.backgroundAudioContext.currentTime;

      // Programar los acordes
      chords.forEach((chord, index) => {
        playChord(chord, currentTime + (index * chordDuration), chordDuration);
      });

      // Programar repetición
      setTimeout(() => {
        if (this.isMusicPlaying) {
          this.startWebAudioBackgroundMusic();
        }
      }, chords.length * chordDuration * 1000);

    } catch (error) {
      console.error('Error starting web audio background music:', error);
    }
  }

  async stopBackgroundMusic() {
    if (!this.isMusicPlaying) return;

    try {
      console.log('Stopping background music');
      this.isMusicPlaying = false;

      if (this.backgroundAudioContext) {
        this.backgroundAudioContext.close();
        this.backgroundAudioContext = null;
      }
    } catch (error) {
      console.error('Error stopping background music:', error);
    }
  }

  setEnabled(enabled) {
    this.isEnabled = enabled;
    if (!enabled && this.isMusicPlaying) {
      this.stopBackgroundMusic();
    }
  }

  async cleanup() {
    try {
      // Detener música de fondo
      await this.stopBackgroundMusic();

      // Limpiar contexto de audio
      if (this.backgroundAudioContext) {
        this.backgroundAudioContext.close();
        this.backgroundAudioContext = null;
      }

      this.sounds = {};
      this.backgroundMusic = null;
      this.isInitialized = false;
      this.isMusicPlaying = false;
    } catch (error) {
      console.error('Error cleaning up sounds:', error);
    }
  }
}

export default new SoundManager();
