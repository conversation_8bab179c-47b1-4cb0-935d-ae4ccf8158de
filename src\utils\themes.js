export const getThemeColors = (theme) => {
  const themes = {
    zen: {
      background: ['#E8F5E8', '#F0F8F0'],
      primary: '#4ECDC4',
      secondary: '#45B7B8',
      card: '#FFFFFF',
      text: '#2C3E50',
      textSecondary: '#7F8C8D',
      success: '#27AE60',
      error: '#E74C3C',
      warning: '#F39C12',
      shadow: 'rgba(0,0,0,0.1)'
    },
    dark: {
      background: ['#1A1A2E', '#16213E'],
      primary: '#0F3460',
      secondary: '#533483',
      card: '#2C2C54',
      text: '#FFFFFF',
      textSecondary: '#BDC3C7',
      success: '#2ECC71',
      error: '#E74C3C',
      warning: '#F39C12',
      shadow: 'rgba(0,0,0,0.3)'
    },
    kids: {
      background: ['#FFE5F1', '#FFF0F8'],
      primary: '#FF6B9D',
      secondary: '#C44569',
      card: '#FFFFFF',
      text: '#2C3E50',
      textSecondary: '#7F8C8D',
      success: '#6BCF7F',
      error: '#FF5722',
      warning: '#FFB74D',
      shadow: 'rgba(255,107,157,0.2)'
    },
    competitive: {
      background: ['#0C0C0C', '#1A1A1A'],
      primary: '#FF4757',
      secondary: '#FF3742',
      card: '#2F3542',
      text: '#FFFFFF',
      textSecondary: '#A4B0BE',
      success: '#2ED573',
      error: '#FF4757',
      warning: '#FFA502',
      shadow: 'rgba(255,71,87,0.3)'
    }
  };

  return themes[theme] || themes.zen;
};

export const getGameItemsByMode = (mode) => {
  const items = {
    colors: {
      items: ['🔴', '🟡', '🟢', '🔵', '🟣', '🟠'],
      colors: ['#FF6B6B', '#FFD93D', '#6BCF7F', '#4ECDC4', '#A8E6CF', '#FFB347']
    },
    sounds: {
      items: ['🔊', '🔉', '🔈', '🔇', '🎵', '🎶'],
      colors: ['#FF6B6B', '#FFD93D', '#6BCF7F', '#4ECDC4', '#A8E6CF', '#FFB347']
    },
    patterns: {
      items: ['⭐', '❤️', '💎', '🌟', '✨', '🎯'],
      colors: ['#FF6B6B', '#FFD93D', '#6BCF7F', '#4ECDC4', '#A8E6CF', '#FFB347']
    }
  };

  return items[mode] || items.colors;
};

export const getDifficultySettings = (difficulty) => {
  const settings = {
    easy: {
      initialSpeed: 3000,
      minSpeed: 1500,
      speedDecrease: 150,
      pointsPerLevel: 150
    },
    normal: {
      initialSpeed: 2000,
      minSpeed: 800,
      speedDecrease: 200,
      pointsPerLevel: 100
    },
    hard: {
      initialSpeed: 1500,
      minSpeed: 500,
      speedDecrease: 250,
      pointsPerLevel: 75
    }
  };

  return settings[difficulty] || settings.normal;
};
