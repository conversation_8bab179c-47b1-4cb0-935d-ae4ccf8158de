import React, { createContext, useContext, useReducer, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import SoundManager from '../utils/SoundManager';

const GameContext = createContext();

// Estados del juego
const GAME_STATES = {
  MENU: 'menu',
  PLAYING: 'playing',
  PAUSED: 'paused',
  GAME_OVER: 'gameOver',
  SETTINGS: 'settings'
};

// Modos de juego
const GAME_MODES = {
  COLORS: 'colors',
  SOUNDS: 'sounds',
  PATTERNS: 'patterns'
};

// Temas
const THEMES = {
  ZEN: 'zen',
  DARK: 'dark',
  KIDS: 'kids',
  COMPETITIVE: 'competitive'
};

// Estado inicial
const initialState = {
  gameState: GAME_STATES.MENU,
  gameMode: GAME_MODES.COLORS,
  theme: THEMES.ZEN,
  score: 0,
  lives: 3,
  level: 1,
  highScores: {
    [GAME_MODES.COLORS]: 0,
    [GAME_MODES.SOUNDS]: 0,
    [GAME_MODES.PATTERNS]: 0
  },
  settings: {
    soundEnabled: true,
    vibrationEnabled: true,
    difficulty: 'normal' // easy, normal, hard
  },
  currentTarget: null,
  currentItem: null,
  gameSpeed: 2000,
  isActive: false,
  feedback: null
};

// Reducer
function gameReducer(state, action) {
  switch (action.type) {
    case 'SET_GAME_STATE':
      return { ...state, gameState: action.payload };
    
    case 'SET_GAME_MODE':
      return { ...state, gameMode: action.payload };
    
    case 'SET_THEME':
      return { ...state, theme: action.payload };
    
    case 'START_GAME':
      return {
        ...state,
        gameState: GAME_STATES.PLAYING,
        score: 0,
        lives: 3,
        level: 1,
        gameSpeed: state.settings.difficulty === 'easy' ? 3000 : 
                   state.settings.difficulty === 'hard' ? 1500 : 2000,
        feedback: null
      };
    
    case 'UPDATE_SCORE':
      return { ...state, score: state.score + action.payload };
    
    case 'LOSE_LIFE':
      const newLives = state.lives - 1;
      return {
        ...state,
        lives: newLives,
        gameState: newLives <= 0 ? GAME_STATES.GAME_OVER : state.gameState
      };
    
    case 'LEVEL_UP':
      return {
        ...state,
        level: state.level + 1,
        gameSpeed: Math.max(800, state.gameSpeed - 200)
      };
    
    case 'SET_HIGH_SCORE':
      return {
        ...state,
        highScores: {
          ...state.highScores,
          [action.gameMode]: action.score
        }
      };
    
    case 'UPDATE_SETTINGS':
      return {
        ...state,
        settings: { ...state.settings, ...action.payload }
      };
    
    case 'SET_CURRENT_ITEMS':
      return {
        ...state,
        currentTarget: action.target,
        currentItem: action.item,
        isActive: true
      };
    
    case 'SET_ACTIVE':
      return { ...state, isActive: action.payload };
    
    case 'SET_FEEDBACK':
      return { ...state, feedback: action.payload };
    
    case 'RESET_GAME':
      return {
        ...state,
        score: 0,
        lives: 3,
        level: 1,
        gameSpeed: state.settings.difficulty === 'easy' ? 3000 : 
                   state.settings.difficulty === 'hard' ? 1500 : 2000,
        currentTarget: null,
        currentItem: null,
        isActive: false,
        feedback: null
      };
    
    case 'LOAD_DATA':
      return { ...state, ...action.payload };
    
    default:
      return state;
  }
}

// Provider
export function GameProvider({ children }) {
  const [state, dispatch] = useReducer(gameReducer, initialState);

  // Cargar datos guardados al iniciar
  useEffect(() => {
    loadGameData();
    initializeSounds();
  }, []);

  // Inicializar sistema de sonido
  const initializeSounds = async () => {
    try {
      await SoundManager.initialize();
      SoundManager.setEnabled(state.settings.soundEnabled);
    } catch (error) {
      console.error('Error initializing sounds:', error);
    }
  };

  // Guardar datos cuando cambien los high scores o settings
  useEffect(() => {
    saveGameData();
    // Actualizar configuración de sonido
    if (SoundManager.isInitialized) {
      SoundManager.setEnabled(state.settings.soundEnabled);
    }
  }, [state.highScores, state.settings, state.theme]);

  const loadGameData = async () => {
    try {
      const savedData = await AsyncStorage.getItem('reflejoInversoData');
      if (savedData) {
        const parsedData = JSON.parse(savedData);
        dispatch({ type: 'LOAD_DATA', payload: parsedData });
      }
    } catch (error) {
      console.error('Error loading game data:', error);
    }
  };

  const saveGameData = async () => {
    try {
      const dataToSave = {
        highScores: state.highScores,
        settings: state.settings,
        theme: state.theme
      };
      await AsyncStorage.setItem('reflejoInversoData', JSON.stringify(dataToSave));
    } catch (error) {
      console.error('Error saving game data:', error);
    }
  };

  const value = {
    state,
    dispatch,
    GAME_STATES,
    GAME_MODES,
    THEMES,
    SoundManager
  };

  return (
    <GameContext.Provider value={value}>
      {children}
    </GameContext.Provider>
  );
}

// Hook personalizado
export function useGame() {
  const context = useContext(GameContext);
  if (!context) {
    throw new Error('useGame must be used within a GameProvider');
  }
  return context;
}
