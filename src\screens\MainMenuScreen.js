import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  ScrollView,
  Modal
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useGame } from '../context/GameContext';
import { getThemeColors } from '../utils/themes';

const { width, height } = Dimensions.get('window');

export default function MainMenuScreen({ navigation }) {
  const { state, dispatch, GAME_MODES, THEMES } = useGame();
  const [showInstructions, setShowInstructions] = useState(false);
  const themeColors = getThemeColors(state.theme);

  const gameModes = [
    {
      id: GAME_MODES.COLORS,
      name: 'Colores',
      description: 'NO toques el color correcto\nSÍ toca el incorrecto',
      icon: '🎨',
      gradient: ['#FF6B6B', '#4ECDC4']
    },
    {
      id: GAME_MODES.SOUNDS,
      name: 'Son<PERSON><PERSON>',
      description: 'NO toques cuando suene\nel tono correcto',
      icon: '🔊',
      gradient: ['#A8E6CF', '#88D8C0']
    },
    {
      id: GAME_MODES.PATTERNS,
      name: 'Patrones',
      description: 'Secuencias engañosas\npara confundir tu mente',
      icon: '🧩',
      gradient: ['#FFD93D', '#6BCF7F']
    }
  ];

  const themes = [
    { id: THEMES.ZEN, name: 'Zen', icon: '🧘' },
    { id: THEMES.DARK, name: 'Oscuro', icon: '🌙' },
    { id: THEMES.KIDS, name: 'Infantil', icon: '🌈' },
    { id: THEMES.COMPETITIVE, name: 'Competitivo', icon: '⚡' }
  ];

  const startGame = (mode) => {
    dispatch({ type: 'SET_GAME_MODE', payload: mode });
    dispatch({ type: 'START_GAME' });
    navigation.navigate('Game');
  };

  const changeTheme = (theme) => {
    dispatch({ type: 'SET_THEME', payload: theme });
  };

  return (
    <LinearGradient
      colors={themeColors.background}
      style={styles.container}
    >
      <SafeAreaView style={styles.safeArea}>
        <ScrollView contentContainerStyle={styles.scrollContent}>
          <View style={styles.header}>
            <Text style={[styles.title, { color: themeColors.text }]}>
              <Text style={styles.titleMain}>Reflejo</Text>
              {'\n'}
              <Text style={styles.titleInverse}>Inverso</Text>
            </Text>
            <Text style={[styles.subtitle, { color: themeColors.textSecondary }]}>
              ¡Piensa al revés para ganar!
            </Text>
          </View>

          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: themeColors.text }]}>
              Elige tu modo de juego
            </Text>
            <View style={styles.modesContainer}>
              {gameModes.map((mode) => (
                <TouchableOpacity
                  key={mode.id}
                  style={styles.modeButton}
                  onPress={() => startGame(mode.id)}
                  activeOpacity={0.8}
                >
                  <LinearGradient
                    colors={mode.gradient}
                    style={styles.modeGradient}
                  >
                    <Text style={styles.modeIcon}>{mode.icon}</Text>
                    <Text style={styles.modeName}>{mode.name}</Text>
                    <Text style={styles.modeDescription}>{mode.description}</Text>
                    <View style={styles.highScoreContainer}>
                      <Text style={styles.highScoreLabel}>Récord:</Text>
                      <Text style={styles.highScoreValue}>
                        {state.highScores[mode.id]}
                      </Text>
                    </View>
                  </LinearGradient>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: themeColors.text }]}>
              Tema
            </Text>
            <View style={styles.themesContainer}>
              {themes.map((theme) => (
                <TouchableOpacity
                  key={theme.id}
                  style={[
                    styles.themeButton,
                    { backgroundColor: themeColors.card },
                    state.theme === theme.id && { 
                      backgroundColor: themeColors.primary,
                      transform: [{ scale: 1.1 }]
                    }
                  ]}
                  onPress={() => changeTheme(theme.id)}
                  activeOpacity={0.7}
                >
                  <Text style={styles.themeIcon}>{theme.icon}</Text>
                  <Text style={[
                    styles.themeName,
                    { color: state.theme === theme.id ? '#fff' : themeColors.text }
                  ]}>
                    {theme.name}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          <View style={styles.actionsContainer}>
            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: themeColors.card }]}
              onPress={() => setShowInstructions(true)}
              activeOpacity={0.7}
            >
              <Text style={styles.actionIcon}>❓</Text>
              <Text style={[styles.actionText, { color: themeColors.text }]}>
                ¿Cómo jugar?
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: themeColors.card }]}
              onPress={() => navigation.navigate('Settings')}
              activeOpacity={0.7}
            >
              <Text style={styles.actionIcon}>⚙️</Text>
              <Text style={[styles.actionText, { color: themeColors.text }]}>
                Configuración
              </Text>
            </TouchableOpacity>
          </View>
        </ScrollView>

        <Modal
          visible={showInstructions}
          animationType="slide"
          transparent={true}
          onRequestClose={() => setShowInstructions(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={[styles.modalContent, { backgroundColor: themeColors.card }]}>
              <Text style={[styles.modalTitle, { color: themeColors.text }]}>
                ¿Cómo jugar Reflejo Inverso?
              </Text>
              
              <ScrollView style={styles.instructionsScroll}>
                <View style={styles.instructionStep}>
                  <Text style={styles.stepNumber}>1</Text>
                  <View style={styles.stepContent}>
                    <Text style={[styles.stepTitle, { color: themeColors.text }]}>
                      Lógica Inversa
                    </Text>
                    <Text style={[styles.stepDescription, { color: themeColors.textSecondary }]}>
                      En este juego, debes hacer lo CONTRARIO de lo que normalmente harías.
                    </Text>
                  </View>
                </View>

                <View style={styles.instructionStep}>
                  <Text style={styles.stepNumber}>2</Text>
                  <View style={styles.stepContent}>
                    <Text style={[styles.stepTitle, { color: themeColors.text }]}>
                      NO toques lo correcto
                    </Text>
                    <Text style={[styles.stepDescription, { color: themeColors.textSecondary }]}>
                      Cuando aparezca el color/sonido objetivo, ¡NO lo toques! Espera.
                    </Text>
                  </View>
                </View>

                <View style={styles.instructionStep}>
                  <Text style={styles.stepNumber}>3</Text>
                  <View style={styles.stepContent}>
                    <Text style={[styles.stepTitle, { color: themeColors.text }]}>
                      SÍ toca lo incorrecto
                    </Text>
                    <Text style={[styles.stepDescription, { color: themeColors.textSecondary }]}>
                      Solo toca cuando aparezca algo diferente al objetivo.
                    </Text>
                  </View>
                </View>

                <View style={styles.instructionStep}>
                  <Text style={styles.stepNumber}>4</Text>
                  <View style={styles.stepContent}>
                    <Text style={[styles.stepTitle, { color: themeColors.text }]}>
                      Mantén la concentración
                    </Text>
                    <Text style={[styles.stepDescription, { color: themeColors.textSecondary }]}>
                      La velocidad aumenta y tu cerebro querrá hacer lo "normal". ¡Resiste!
                    </Text>
                  </View>
                </View>
              </ScrollView>

              <TouchableOpacity
                style={[styles.closeButton, { backgroundColor: themeColors.primary }]}
                onPress={() => setShowInstructions(false)}
                activeOpacity={0.8}
              >
                <Text style={styles.closeButtonText}>Entendido</Text>
              </TouchableOpacity>
            </View>
          </View>
        </Modal>
      </SafeAreaView>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: 20,
  },
  header: {
    alignItems: 'center',
    marginTop: 20,
    marginBottom: 30,
  },
  title: {
    fontSize: 48,
    fontWeight: 'bold',
    textAlign: 'center',
    textShadowColor: 'rgba(0,0,0,0.3)',
    textShadowOffset: { width: 2, height: 2 },
    textShadowRadius: 4,
  },
  titleMain: {
    fontSize: 48,
  },
  titleInverse: {
    fontSize: 48,
    fontStyle: 'italic',
  },
  subtitle: {
    fontSize: 18,
    marginTop: 10,
    textAlign: 'center',
    fontWeight: '500',
  },
  section: {
    marginBottom: 30,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 15,
    textAlign: 'center',
  },
  modesContainer: {
    gap: 15,
  },
  modeButton: {
    borderRadius: 20,
    overflow: 'hidden',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  modeGradient: {
    padding: 20,
    alignItems: 'center',
    minHeight: 140,
    justifyContent: 'center',
  },
  modeIcon: {
    fontSize: 40,
    marginBottom: 8,
  },
  modeName: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 5,
    textShadowColor: 'rgba(0,0,0,0.5)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  modeDescription: {
    fontSize: 14,
    color: '#fff',
    textAlign: 'center',
    lineHeight: 18,
    marginBottom: 10,
    textShadowColor: 'rgba(0,0,0,0.5)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  highScoreContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 15,
  },
  highScoreLabel: {
    fontSize: 12,
    color: '#fff',
    marginRight: 5,
  },
  highScoreValue: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#fff',
  },
  themesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-around',
    gap: 10,
  },
  themeButton: {
    width: (width - 60) / 2,
    padding: 15,
    borderRadius: 15,
    alignItems: 'center',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
  },
  themeIcon: {
    fontSize: 24,
    marginBottom: 5,
  },
  themeName: {
    fontSize: 16,
    fontWeight: '600',
  },
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 20,
    gap: 15,
  },
  actionButton: {
    flex: 1,
    padding: 15,
    borderRadius: 15,
    alignItems: 'center',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
  },
  actionIcon: {
    fontSize: 24,
    marginBottom: 5,
  },
  actionText: {
    fontSize: 14,
    fontWeight: '600',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: width * 0.9,
    maxHeight: height * 0.8,
    borderRadius: 20,
    padding: 20,
    elevation: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 5 },
    shadowOpacity: 0.34,
    shadowRadius: 6.27,
  },
  modalTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
  },
  instructionsScroll: {
    maxHeight: height * 0.5,
  },
  instructionStep: {
    flexDirection: 'row',
    marginBottom: 20,
    alignItems: 'flex-start',
  },
  stepNumber: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: '#4ECDC4',
    color: '#fff',
    textAlign: 'center',
    lineHeight: 30,
    fontSize: 16,
    fontWeight: 'bold',
    marginRight: 15,
  },
  stepContent: {
    flex: 1,
  },
  stepTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  stepDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  closeButton: {
    marginTop: 20,
    padding: 15,
    borderRadius: 15,
    alignItems: 'center',
  },
  closeButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
});
