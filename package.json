{"name": "reflejo-inverso", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.2.0", "@react-navigation/native": "^7.1.14", "@react-navigation/stack": "^7.4.2", "expo": "~53.0.16", "expo-av": "^15.1.7", "expo-haptics": "^14.1.4", "expo-linear-gradient": "^14.1.5", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "0.79.5", "react-native-gesture-handler": "^2.27.1", "react-native-safe-area-context": "^5.5.1", "react-native-screens": "^4.11.1"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}