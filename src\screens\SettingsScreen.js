import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Switch
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useGame } from '../context/GameContext';
import { getThemeColors } from '../utils/themes';

export default function SettingsScreen({ navigation }) {
  const { state, dispatch, SoundManager } = useGame();
  const themeColors = getThemeColors(state.theme);

  const updateSetting = (key, value) => {
    dispatch({ 
      type: 'UPDATE_SETTINGS', 
      payload: { [key]: value } 
    });
  };

  const difficulties = [
    { id: 'easy', name: '<PERSON><PERSON><PERSON><PERSON>', description: 'Más tiempo para pensar' },
    { id: 'normal', name: 'Normal', description: 'Equilibrio perfecto' },
    { id: 'hard', name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', description: 'Para expertos' }
  ];

  return (
    <LinearGradient
      colors={themeColors.background}
      style={styles.container}
    >
      <SafeAreaView style={styles.safeArea}>
        <View style={styles.header}>
          <TouchableOpacity
            style={[styles.backButton, { backgroundColor: themeColors.card }]}
            onPress={() => {
              SoundManager.playSound('click');
              navigation.goBack();
            }}
            activeOpacity={0.7}
          >
            <Text style={styles.backIcon}>←</Text>
          </TouchableOpacity>
          <Text style={[styles.title, { color: themeColors.text }]}>
            Configuración
          </Text>
          <View style={styles.placeholder} />
        </View>

        <ScrollView style={styles.content}>
          {/* Sonido */}
          <View style={[styles.settingSection, { backgroundColor: themeColors.card }]}>
            <View style={styles.settingHeader}>
              <Text style={styles.settingIcon}>🔊</Text>
              <View style={styles.settingInfo}>
                <Text style={[styles.settingTitle, { color: themeColors.text }]}>
                  Sonido
                </Text>
                <Text style={[styles.settingDescription, { color: themeColors.textSecondary }]}>
                  Efectos de sonido del juego
                </Text>
              </View>
              <Switch
                value={state.settings.soundEnabled}
                onValueChange={(value) => updateSetting('soundEnabled', value)}
                trackColor={{ false: '#767577', true: themeColors.primary }}
                thumbColor={state.settings.soundEnabled ? '#fff' : '#f4f3f4'}
              />
            </View>
          </View>

          {/* Vibración */}
          <View style={[styles.settingSection, { backgroundColor: themeColors.card }]}>
            <View style={styles.settingHeader}>
              <Text style={styles.settingIcon}>📳</Text>
              <View style={styles.settingInfo}>
                <Text style={[styles.settingTitle, { color: themeColors.text }]}>
                  Vibración
                </Text>
                <Text style={[styles.settingDescription, { color: themeColors.textSecondary }]}>
                  Feedback táctil al tocar
                </Text>
              </View>
              <Switch
                value={state.settings.vibrationEnabled}
                onValueChange={(value) => updateSetting('vibrationEnabled', value)}
                trackColor={{ false: '#767577', true: themeColors.primary }}
                thumbColor={state.settings.vibrationEnabled ? '#fff' : '#f4f3f4'}
              />
            </View>
          </View>

          {/* Dificultad */}
          <View style={[styles.settingSection, { backgroundColor: themeColors.card }]}>
            <View style={styles.settingHeader}>
              <Text style={styles.settingIcon}>⚡</Text>
              <View style={styles.settingInfo}>
                <Text style={[styles.settingTitle, { color: themeColors.text }]}>
                  Dificultad
                </Text>
                <Text style={[styles.settingDescription, { color: themeColors.textSecondary }]}>
                  Velocidad del juego
                </Text>
              </View>
            </View>
            
            <View style={styles.difficultyOptions}>
              {difficulties.map((difficulty) => (
                <TouchableOpacity
                  key={difficulty.id}
                  style={[
                    styles.difficultyButton,
                    { 
                      backgroundColor: state.settings.difficulty === difficulty.id ? 
                        themeColors.primary : 'transparent',
                      borderColor: themeColors.primary
                    }
                  ]}
                  onPress={() => {
                    SoundManager.playSound('click');
                    updateSetting('difficulty', difficulty.id);
                  }}
                  activeOpacity={0.7}
                >
                  <Text style={[
                    styles.difficultyName,
                    { 
                      color: state.settings.difficulty === difficulty.id ? 
                        '#fff' : themeColors.text 
                    }
                  ]}>
                    {difficulty.name}
                  </Text>
                  <Text style={[
                    styles.difficultyDescription,
                    { 
                      color: state.settings.difficulty === difficulty.id ? 
                        'rgba(255,255,255,0.8)' : themeColors.textSecondary 
                    }
                  ]}>
                    {difficulty.description}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Estadísticas */}
          <View style={[styles.settingSection, { backgroundColor: themeColors.card }]}>
            <View style={styles.settingHeader}>
              <Text style={styles.settingIcon}>📊</Text>
              <View style={styles.settingInfo}>
                <Text style={[styles.settingTitle, { color: themeColors.text }]}>
                  Récords Personales
                </Text>
                <Text style={[styles.settingDescription, { color: themeColors.textSecondary }]}>
                  Tus mejores puntuaciones
                </Text>
              </View>
            </View>
            
            <View style={styles.statsContainer}>
              <View style={styles.statRow}>
                <Text style={[styles.statLabel, { color: themeColors.text }]}>
                  🎨 Colores:
                </Text>
                <Text style={[styles.statValue, { color: themeColors.primary }]}>
                  {state.highScores.colors}
                </Text>
              </View>
              
              <View style={styles.statRow}>
                <Text style={[styles.statLabel, { color: themeColors.text }]}>
                  🔊 Sonidos:
                </Text>
                <Text style={[styles.statValue, { color: themeColors.primary }]}>
                  {state.highScores.sounds}
                </Text>
              </View>
              
              <View style={styles.statRow}>
                <Text style={[styles.statLabel, { color: themeColors.text }]}>
                  🧩 Patrones:
                </Text>
                <Text style={[styles.statValue, { color: themeColors.primary }]}>
                  {state.highScores.patterns}
                </Text>
              </View>
            </View>
          </View>

          {/* Información */}
          <View style={[styles.settingSection, { backgroundColor: themeColors.card }]}>
            <View style={styles.settingHeader}>
              <Text style={styles.settingIcon}>ℹ️</Text>
              <View style={styles.settingInfo}>
                <Text style={[styles.settingTitle, { color: themeColors.text }]}>
                  Acerca del juego
                </Text>
                <Text style={[styles.settingDescription, { color: themeColors.textSecondary }]}>
                  Reflejo Inverso v1.0
                </Text>
              </View>
            </View>
            
            <View style={styles.infoContainer}>
              <Text style={[styles.infoText, { color: themeColors.textSecondary }]}>
                Un juego que desafía tu mente con lógica inversa.{'\n'}
                Desarrollado con React Native y Expo.
              </Text>
            </View>
          </View>
        </ScrollView>
      </SafeAreaView>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
  },
  backIcon: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  settingSection: {
    borderRadius: 15,
    padding: 20,
    marginBottom: 15,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
  },
  settingHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  settingIcon: {
    fontSize: 24,
    marginRight: 15,
  },
  settingInfo: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  settingDescription: {
    fontSize: 14,
  },
  difficultyOptions: {
    marginTop: 15,
    gap: 10,
  },
  difficultyButton: {
    padding: 15,
    borderRadius: 10,
    borderWidth: 2,
  },
  difficultyName: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  difficultyDescription: {
    fontSize: 12,
  },
  statsContainer: {
    marginTop: 15,
    gap: 10,
  },
  statRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  statLabel: {
    fontSize: 16,
    fontWeight: '500',
  },
  statValue: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  infoContainer: {
    marginTop: 15,
  },
  infoText: {
    fontSize: 14,
    lineHeight: 20,
    textAlign: 'center',
  },
});
